"""
测试 VideoTrendService 修复
"""

import pytest
from unittest.mock import AsyncMock, patch

from services.trendinsight.video_trend_service import VideoTrendService
from rpc.trendinsight.schemas.video import ItemIndexExistResponse, ItemIndexExistData
from rpc.trendinsight.schemas.base import DarenBaseResp


class TestVideoTrendServiceFix:
    """测试 VideoTrendService 修复"""

    @pytest.mark.asyncio
    async def test_fetch_and_store_video_trend_score_success(self):
        """测试成功获取和存储视频趋势评分"""
        aweme_id = "7476361017224662324"
        
        # 模拟成功的响应
        mock_response = ItemIndexExistResponse(
            data=ItemIndexExistData(
                item_statue="succeed",
                item_index="237",
                BaseResp=DarenBaseResp(StatusMessage="", StatusCode=0)
            ),
            msg="",
            status=0
        )
        
        # 模拟数据库更新成功
        with patch('services.trendinsight.video_trend_service.client_manager.create_async_client') as mock_client_manager, \
             patch('services.trendinsight.video_trend_service.AsyncTrendInsightAPI') as mock_api_class, \
             patch('services.trendinsight.video_trend_service.update_trendinsight_video') as mock_update:
            
            # 设置 mock
            mock_api = AsyncMock()
            mock_api.get_item_index_exist.return_value = mock_response
            mock_api_class.return_value = mock_api
            mock_update.return_value = True
            
            # 调用方法
            result = await VideoTrendService.fetch_and_store_video_trend_score(aweme_id)
            
            # 验证结果
            assert result is True
            mock_api.get_item_index_exist.assert_called_once_with(item_id=aweme_id)
            mock_update.assert_called_once_with({"aweme_id": aweme_id, "trend_score": 237.0})

    @pytest.mark.asyncio
    async def test_fetch_and_store_video_trend_score_failed_status(self):
        """测试视频状态为失败的情况"""
        aweme_id = "7476361017224662324"
        
        # 模拟失败的响应（没有 item_index 字段）
        mock_response = ItemIndexExistResponse(
            data=ItemIndexExistData(
                item_statue="failed",
                item_index=None,
                BaseResp=DarenBaseResp(StatusMessage="", StatusCode=0)
            ),
            msg="",
            status=0
        )
        
        with patch('services.trendinsight.video_trend_service.client_manager.create_async_client') as mock_client_manager, \
             patch('services.trendinsight.video_trend_service.AsyncTrendInsightAPI') as mock_api_class:
            
            # 设置 mock
            mock_api = AsyncMock()
            mock_api.get_item_index_exist.return_value = mock_response
            mock_api_class.return_value = mock_api
            
            # 调用方法
            result = await VideoTrendService.fetch_and_store_video_trend_score(aweme_id)
            
            # 验证结果
            assert result is False
            mock_api.get_item_index_exist.assert_called_once_with(item_id=aweme_id)

    @pytest.mark.asyncio
    async def test_fetch_and_store_video_trend_score_no_item_index(self):
        """测试没有指数数据的情况"""
        aweme_id = "7476361017224662324"
        
        # 模拟成功响应但没有指数数据
        mock_response = ItemIndexExistResponse(
            data=ItemIndexExistData(
                item_statue="succeed",
                item_index=None,
                BaseResp=DarenBaseResp(StatusMessage="", StatusCode=0)
            ),
            msg="",
            status=0
        )
        
        with patch('services.trendinsight.video_trend_service.client_manager.create_async_client') as mock_client_manager, \
             patch('services.trendinsight.video_trend_service.AsyncTrendInsightAPI') as mock_api_class:
            
            # 设置 mock
            mock_api = AsyncMock()
            mock_api.get_item_index_exist.return_value = mock_response
            mock_api_class.return_value = mock_api
            
            # 调用方法
            result = await VideoTrendService.fetch_and_store_video_trend_score(aweme_id)
            
            # 验证结果
            assert result is False
            mock_api.get_item_index_exist.assert_called_once_with(item_id=aweme_id)

    @pytest.mark.asyncio
    async def test_fetch_and_store_video_trend_score_invalid_index_value(self):
        """测试无效指数值的情况"""
        aweme_id = "7476361017224662324"
        
        # 模拟响应包含无效的指数值
        mock_response = ItemIndexExistResponse(
            data=ItemIndexExistData(
                item_statue="succeed",
                item_index="invalid_number",
                BaseResp=DarenBaseResp(StatusMessage="", StatusCode=0)
            ),
            msg="",
            status=0
        )
        
        with patch('services.trendinsight.video_trend_service.client_manager.create_async_client') as mock_client_manager, \
             patch('services.trendinsight.video_trend_service.AsyncTrendInsightAPI') as mock_api_class, \
             patch('services.trendinsight.video_trend_service.update_trendinsight_video') as mock_update:
            
            # 设置 mock
            mock_api = AsyncMock()
            mock_api.get_item_index_exist.return_value = mock_response
            mock_api_class.return_value = mock_api
            mock_update.return_value = True
            
            # 调用方法
            result = await VideoTrendService.fetch_and_store_video_trend_score(aweme_id)
            
            # 验证结果 - 应该使用默认值 0.0
            assert result is True
            mock_api.get_item_index_exist.assert_called_once_with(item_id=aweme_id)
            mock_update.assert_called_once_with({"aweme_id": aweme_id, "trend_score": 0.0})
